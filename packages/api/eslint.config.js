import eslintJs from "@eslint/js";
import eslint<PERSON>onfig<PERSON>rettier from "eslint-config-prettier";
import typescriptEslint from "typescript-eslint";

const config = [
  // Base JavaScript recommended rules
  eslintJs.configs.recommended,

  // TypeScript configuration
  ...typescriptEslint.configs.recommended,

  // Global ignores
  {
    ignores: [
      "src/database/__generated__/**",
      "src/types/cloudflare-worker.d.ts",
    ],
  },

  // Only lint TypeScript files in src directory
  {
    files: ["src/**/*.ts"],
    languageOptions: {
      parserOptions: {
        project: "./tsconfig.json",
        tsconfigRootDir: import.meta.dirname,
      },
    },
  },

  // Prettier config should be last to override conflicting rules
  eslintConfigPrettier,
];

export default config;
