{"name": "@rackup/api", "version": "1.0.0-alpha.0", "private": true, "license": "UNLICENSED", "type": "module", "scripts": {"cloudflare:worker:types": "wrangler types src/types/cloudflare-worker.d.ts", "database:deploy": "prisma migrate deploy", "database:migrate": "prisma migrate dev", "database:reset": "prisma migrate reset", "database:seed": "prisma db seed", "format:check": "prettier . --check", "format:fix": "prettier . --write", "lint:check": "eslint .", "lint:fix": "eslint . --fix", "start": "wrangler dev --env local"}, "dependencies": {"@graphql-yoga/plugin-jwt": "3.9.1", "@pothos/core": "4.8.1", "@pothos/plugin-prisma": "4.10.0", "@pothos/plugin-with-input": "4.1.3", "@prisma/adapter-pg": "6.14.0", "@prisma/client": "6.14.0", "graphql": "16.11.0", "graphql-scalars": "1.24.2", "graphql-yoga": "5.15.1", "prisma": "6.14.0"}, "devDependencies": {"@eslint/js": "9.33.0", "@ianvs/prettier-plugin-sort-imports": "4.6.1", "@types/node": "^22", "dotenv": "17.2.1", "eslint": "9.33.0", "eslint-config-prettier": "10.1.8", "prettier": "3.6.2", "prettier-plugin-packagejson": "2.5.19", "tsx": "4.20.4", "typescript": "5.9.2", "typescript-eslint": "8.39.1", "wrangler": "4.29.0"}, "packageManager": "pnpm@10.14.0", "engines": {"node": "^22"}}