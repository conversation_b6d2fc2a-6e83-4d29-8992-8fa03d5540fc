name: Checks
description: Run format, lint, and type checks for a package
inputs:
  package:
    description: The package to run checks for
    required: true
runs:
  using: composite
  steps:
    - name: Format Check
      run: pnpm --filter ${{ inputs.package }} run format:check
      shell: bash
    - name: Lint Check
      run: pnpm --filter ${{ inputs.package }} run lint:check
      shell: bash
    - name: Type Check
      run: pnpm --filter ${{ inputs.package }} run type:check
      shell: bash
